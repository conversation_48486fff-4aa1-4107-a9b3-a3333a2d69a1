"""
Test script for Unipile API integration
"""

import json
from unipile_api import UnipileAPI
from unified_messaging import UnifiedMessaging, MessageRecipient

def test_unipile_connection():
    """Test basic Unipile API connection"""
    print("🔍 Testing Unipile API connection...")
    
    try:
        unipile = UnipileAPI()
        
        # Test getting accounts
        accounts = unipile.get_accounts()
        
        if "error" in accounts:
            print(f"❌ Error connecting to Unipile: {accounts['error']}")
            return False
        
        print("✅ Successfully connected to Unipile API")
        print(f"📊 Response: {json.dumps(accounts, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception testing Unipile: {e}")
        return False

def test_platform_status():
    """Test platform connection status"""
    print("\n🔍 Testing platform connection status...")
    
    try:
        unipile = UnipileAPI()
        status = unipile.get_connection_status()
        
        print("📱 Platform Status:")
        for platform, connected in status.items():
            emoji = "✅" if connected else "❌"
            print(f"  {emoji} {platform.capitalize()}: {'Connected' if connected else 'Not Connected'}")
        
        return status
        
    except Exception as e:
        print(f"❌ Exception checking platform status: {e}")
        return {}

def test_unified_messaging():
    """Test unified messaging system with Unipile"""
    print("\n🔍 Testing unified messaging system...")
    
    try:
        # Initialize with Unipile enabled
        unified = UnifiedMessaging(use_unipile=True)
        
        # Check platform status
        status = unified.get_platform_status()
        print(f"📊 Platform status via unified system: {status}")
        
        # Test message preparation (don't actually send)
        recipients = [
            MessageRecipient(platform="whatsapp", recipient_id="+**********", name="Test User"),
            MessageRecipient(platform="telegram", recipient_id="@testuser", name="Telegram User")
        ]
        
        print(f"📝 Prepared {len(recipients)} test recipients")
        print("✅ Unified messaging system initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception testing unified messaging: {e}")
        return False

def test_account_details():
    """Test getting detailed account information"""
    print("\n🔍 Testing account details...")
    
    try:
        unipile = UnipileAPI()
        platform_accounts = unipile.get_platform_accounts()
        
        if "error" in platform_accounts:
            print(f"❌ Error getting platform accounts: {platform_accounts['error']}")
            return False
        
        print("📱 Platform Accounts:")
        for platform, account in platform_accounts.items():
            if account:
                print(f"  ✅ {platform.capitalize()}: {account.get('username', account.get('name', account.get('account_id', 'Unknown')))}")
            else:
                print(f"  ❌ {platform.capitalize()}: Not connected")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception getting account details: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Unipile API Integration Tests")
    print("=" * 50)
    
    # Test 1: Basic connection
    connection_ok = test_unipile_connection()
    
    # Test 2: Platform status
    if connection_ok:
        platform_status = test_platform_status()
    
    # Test 3: Account details
    if connection_ok:
        account_details_ok = test_account_details()
    
    # Test 4: Unified messaging
    if connection_ok:
        unified_ok = test_unified_messaging()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"  🔗 API Connection: {'✅ Pass' if connection_ok else '❌ Fail'}")
    
    if connection_ok:
        connected_platforms = sum(1 for connected in platform_status.values() if connected)
        total_platforms = len(platform_status)
        print(f"  📱 Connected Platforms: {connected_platforms}/{total_platforms}")
        
        if connected_platforms > 0:
            print("  🎯 Ready for messaging!")
        else:
            print("  ⚠️  No platforms connected - please connect your accounts via Unipile dashboard")
    
    print("\n💡 Next Steps:")
    if not connection_ok:
        print("  1. Check your Unipile API key and network connection")
        print("  2. Verify the Unipile API endpoint is accessible")
    elif connected_platforms == 0:
        print("  1. Visit the Unipile dashboard to connect your social media accounts")
        print("  2. Follow platform-specific setup instructions")
        print("  3. Re-run this test to verify connections")
    else:
        print("  1. Open unipile_setup.html for detailed configuration")
        print("  2. Test messaging with small groups first")
        print("  3. Use the unified messaging system for bulk operations")

if __name__ == "__main__":
    main()
