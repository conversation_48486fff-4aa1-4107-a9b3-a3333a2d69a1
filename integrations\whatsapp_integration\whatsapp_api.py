"""
WhatsApp Business API Integration
Handles authentication and messaging for WhatsApp Business API
"""

import requests
import json
import time
from typing import Dict, List, Optional
import logging
from datetime import datetime

class WhatsAppAPI:
    def __init__(self, config_path: str = "integrations/whatsapp_integration/config.json"):
        """Initialize WhatsApp API client"""
        self.config_path = config_path
        self.config = self._load_config()
        self.base_url = "https://graph.facebook.com/v18.0"
        self.phone_number_id = self.config.get("phone_number_id")
        self.access_token = self.config.get("access_token")
        self.verify_token = self.config.get("verify_token")
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1  # 1 second between requests
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def verify_webhook(self, verify_token: str, challenge: str) -> Optional[str]:
        """Verify webhook for WhatsApp Business API"""
        if verify_token == self.verify_token:
            return challenge
        return None
    
    def send_text_message(self, to_number: str, message: str) -> Dict:
        """Send text message to WhatsApp number"""
        if not self.phone_number_id or not self.access_token:
            return {"error": "WhatsApp API not configured properly"}
        
        # Format phone number (remove + and spaces)
        formatted_number = to_number.replace("+", "").replace(" ", "").replace("-", "")
        
        data = {
            "messaging_product": "whatsapp",
            "to": formatted_number,
            "type": "text",
            "text": {
                "body": message
            }
        }
        
        endpoint = f"{self.phone_number_id}/messages"
        result = self._make_request("POST", endpoint, data)
        
        if "error" not in result:
            self.logger.info(f"Message sent successfully to {formatted_number}")
        else:
            self.logger.error(f"Failed to send message: {result}")
        
        return result
    
    def send_template_message(self, to_number: str, template_name: str, language_code: str = "en", 
                            parameters: List[str] = None) -> Dict:
        """Send template message to WhatsApp number"""
        if not self.phone_number_id or not self.access_token:
            return {"error": "WhatsApp API not configured properly"}
        
        formatted_number = to_number.replace("+", "").replace(" ", "").replace("-", "")
        
        template_data = {
            "name": template_name,
            "language": {
                "code": language_code
            }
        }
        
        if parameters:
            template_data["components"] = [{
                "type": "body",
                "parameters": [{"type": "text", "text": param} for param in parameters]
            }]
        
        data = {
            "messaging_product": "whatsapp",
            "to": formatted_number,
            "type": "template",
            "template": template_data
        }
        
        endpoint = f"{self.phone_number_id}/messages"
        result = self._make_request("POST", endpoint, data)
        
        if "error" not in result:
            self.logger.info(f"Template message sent successfully to {formatted_number}")
        else:
            self.logger.error(f"Failed to send template message: {result}")
        
        return result
    
    def get_message_status(self, message_id: str) -> Dict:
        """Get status of sent message"""
        endpoint = f"{message_id}"
        return self._make_request("GET", endpoint)
    
    def setup_webhook(self, webhook_url: str) -> Dict:
        """Setup webhook for receiving messages"""
        # This would typically be done through Facebook App settings
        # Implementation depends on your webhook endpoint setup
        self.logger.info(f"Webhook setup instructions: Configure {webhook_url} in Facebook App settings")
        return {"status": "manual_setup_required", "webhook_url": webhook_url}
    
    def is_configured(self) -> bool:
        """Check if WhatsApp API is properly configured"""
        return bool(self.phone_number_id and self.access_token)
    
    def update_config(self, phone_number_id: str = None, access_token: str = None, 
                     verify_token: str = None):
        """Update configuration"""
        if phone_number_id:
            self.config["phone_number_id"] = phone_number_id
            self.phone_number_id = phone_number_id
        
        if access_token:
            self.config["access_token"] = access_token
            self.access_token = access_token
        
        if verify_token:
            self.config["verify_token"] = verify_token
            self.verify_token = verify_token
        
        self._save_config()
        self.logger.info("WhatsApp configuration updated")

# Example usage
if __name__ == "__main__":
    whatsapp = WhatsAppAPI()
    
    # Check if configured
    if not whatsapp.is_configured():
        print("WhatsApp API not configured. Please update config.json with your credentials.")
    else:
        # Example: Send a test message
        result = whatsapp.send_text_message("+1234567890", "Hello from WhatsApp Business API!")
        print(f"Message result: {result}")
