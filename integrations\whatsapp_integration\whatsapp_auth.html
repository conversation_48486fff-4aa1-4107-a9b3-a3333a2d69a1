<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Business API Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #25D366;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #25D366;
            background-color: #f9f9f9;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #25D366;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #128C7E;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            border: 1px solid #e9ecef;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🟢 WhatsApp Business API Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> WhatsApp Business API requires approval from Meta and has specific requirements. 
            Make sure you have a verified business and approved WhatsApp Business Account.
        </div>

        <div class="step">
            <h3>Step 1: Create Facebook App</h3>
            <p>1. Go to <a href="https://developers.facebook.com/" target="_blank">Facebook Developers</a></p>
            <p>2. Create a new app and select "Business" type</p>
            <p>3. Add WhatsApp product to your app</p>
            <p>4. Complete business verification process</p>
        </div>

        <div class="step">
            <h3>Step 2: Get Your Credentials</h3>
            <p>From your Facebook App dashboard, collect the following:</p>
            
            <form id="whatsappConfigForm">
                <div class="form-group">
                    <label for="phoneNumberId">Phone Number ID:</label>
                    <input type="text" id="phoneNumberId" name="phoneNumberId" 
                           placeholder="Your WhatsApp Business phone number ID">
                </div>
                
                <div class="form-group">
                    <label for="accessToken">Access Token:</label>
                    <input type="password" id="accessToken" name="accessToken" 
                           placeholder="Your permanent access token">
                </div>
                
                <div class="form-group">
                    <label for="verifyToken">Webhook Verify Token:</label>
                    <input type="text" id="verifyToken" name="verifyToken" 
                           placeholder="Create a secure verify token for webhooks">
                </div>
                
                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>
            
            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 3: Webhook Setup</h3>
            <p>Configure your webhook URL in Facebook App settings:</p>
            <div class="code">
                Webhook URL: https://yourdomain.com/webhook/whatsapp<br>
                Verify Token: [Use the token you entered above]
            </div>
            <p>Subscribe to these webhook fields:</p>
            <ul>
                <li>messages</li>
                <li>message_deliveries</li>
                <li>message_reads</li>
                <li>message_reactions</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 4: Test Your Setup</h3>
            <div class="form-group">
                <label for="testNumber">Test Phone Number (with country code):</label>
                <input type="text" id="testNumber" placeholder="+1234567890">
            </div>
            <div class="form-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from WhatsApp Business API."></textarea>
            </div>
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult"></div>
        </div>
    </div>

    <script>
        function saveConfig() {
            const config = {
                phone_number_id: document.getElementById('phoneNumberId').value,
                access_token: document.getElementById('accessToken').value,
                verify_token: document.getElementById('verifyToken').value
            };
            
            // In a real implementation, this would send to your backend
            fetch('/api/whatsapp/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/whatsapp/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function sendTestMessage() {
            const phoneNumber = document.getElementById('testNumber').value;
            const message = document.getElementById('testMessage').value;
            
            if (!phoneNumber || !message) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Please enter both phone number and message</div>';
                return;
            }
            
            fetch('/api/whatsapp/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    to: phoneNumber,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send test message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
